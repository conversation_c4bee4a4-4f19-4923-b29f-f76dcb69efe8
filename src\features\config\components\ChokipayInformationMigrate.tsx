import { Card, CardContent } from "@/components/ui/card";
import { useState } from "react";
import { GetMerchantStatusResponse } from "../types";
import { Button } from "@/components/ui/button";
import ChokipayInformationNew from "./ChokipayInformationNew";
import ChokipayInformation from "./ChokipayInformation";

interface ChokipayInformationProps {
  merchantData: GetMerchantStatusResponse;
  merchantCrepicoData: GetMerchantStatusResponse;
}

enum TypeStore {
  ALL = 1,
  PAYGATE = 2,
  CREPICO = 3
}

function ChokipayInformationMigrate({ merchantData, merchantCrepicoData }: ChokipayInformationProps) {
  const [typeStoreSelect, setTypeStoreSelect] = useState(TypeStore.ALL);

  return (
    <Card className="border-0 shadow-none">
      <CardContent className="py-8 pl-4 space-y-8">
        { typeStoreSelect === TypeStore.ALL && 
          <div className="wrapper-body py-10 px-8 md:py-10 md:px-10 lg:py-10 lg:px-16 xl:py-10 xl:px-20">
              <div className="grid grid-cols-1 xl:grid-cols-2 xl:gap-x-72 gap-y-20 items-center pr-20">
                  <Button 
                    onClick={() => setTypeStoreSelect(TypeStore.PAYGATE)}
                    className="bg-white hover:bg-white flex items-center justify-center w-full h-44 border-2 border-gray-400 rounded-lg shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl">
                      PAYGATE
                  </Button>

                  <Button 
                    onClick={() => setTypeStoreSelect(TypeStore.CREPICO)}
                    className="bg-white hover:bg-white flex items-center justify-center w-full h-44 border-2 border-gray-400 rounded-lg shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl">
                      クレピコ
                  </Button>
              </div>
          </div>
        }

        { typeStoreSelect === TypeStore.PAYGATE && <ChokipayInformation merchantData={merchantData}/>}

        { typeStoreSelect === TypeStore.CREPICO && <ChokipayInformationNew merchantData={merchantCrepicoData} />}
      </CardContent>
    </Card>
  );
}

export default ChokipayInformationMigrate;
