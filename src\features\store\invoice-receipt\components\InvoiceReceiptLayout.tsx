import React from 'react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { useStoreInvoice } from '../hooks/useStoreInvoice';
import { useAuthStore } from '@/store';
import CreditCardNoticeDialog from './CreditCardNoticeDialog';
import { Notification } from '@/components/Notification';
import { AccountTypes } from '@/types/globalType';

interface InvoiceLayoutProps {
  title: string;
  loading?: boolean;
  error?: string;
  children: React.ReactNode;
  showNotice?: boolean;
}

export const InvoiceLayout = ({
  title,
  loading = false,
  error,
  children,
  showNotice = true,
}: InvoiceLayoutProps) => {
  const { handleCreateLinkplusUrl, handlePaymentGMO, gmoData, open, setOpen } = useStoreInvoice();
  const { user } = useAuthStore();
  
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="min-h-[calc(100vh-180px)] p-4 md:py-6 md:px-1 xl:p-6 text-[20px]">
      {/* Error Display */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-600">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="space-y-6">
        <p className="text-[#707070] text-[24px]">
          {title}
        </p>
      </div>

      <hr className="mt-5 mb-4 border-[#707070]" />

      {/* Notice Section */}
      {showNotice && <Notification isPageOverview={false} />}

      {
        (user && user?.statusAccount == AccountTypes.APPLICATION_COMPLETE) || (!gmoData.memberId || !gmoData.cardNo) && (
          <div className="w-full xl:w-[60%] text-center gap-2 text-base sm:text-lg md:text-xl lg:text-[24px] mb-3 bg-[#1D9987] text-white px-3 sm:px-4 py-2 mt-6 sm:mt-8 md:mt-10 rounded-sm cursor-pointer"
            onClick={() => setOpen(true)}
          >
            月額費用のクレジットカード払いが可能に！
            <br />
            登録はこちら
          </div>
        )
      }

      <CreditCardNoticeDialog
        open={open}
        onOpenChange={setOpen}
        onCancel={() => setOpen(false)}
        onConfirm={handlePaymentGMO}
      />

      {/* Main Content */}
      {children}

      {/* GMO Section */}
      {
        (gmoData && gmoData?.memberId && gmoData?.cardNo) && (
          <div className='mt-8'>
          {/* Header */}
          <div className='bg-gray-500 text-white px-4 py-3 text-[26px]'>
              <h2 className='text-lg font-medium'>チョキペイの月額費用等がクレジットカード支払いが可能に！</h2>
          </div>
          
          {/* Content */}
          <div className='py-4 space-y-3'>
              <p className='text-[#6F6F6E] text-[16px] leading-relaxed font-medium'>
                  クレジットカード登録をすると、チョキペイの月額費用等の支払いがクレジットカードからの引き落としが可能になります。
              </p>
              <p className='text-[#6F6F6E] text-[16px] leading-relaxed font-medium'>
                  （クレジットカード登録は、GMOペイメントゲートウェイのページで行います）
              </p>
              <p className='text-[#C44546] text-[16px] leading-relaxed font-medium'>
                  ※ 「入力したクレジットカード情報を保存する場合はチェックしてください。」に必ずチェックを入れてください。
              </p>
              <p className='text-[#C44546] text-[16px] leading-relaxed font-medium'>
                  ※ カードの有効性を確認するために1円の決済を行いますが、ご請求は発生致しません。
              </p>
              
              {gmoData && (gmoData.memberId !== null || gmoData.cardNo !== null) && (
                <div className='space-y-2 mt-4'>
                  <p className='text-[#6F6F6E] text-[16px] font-medium'>会員ID: {gmoData.memberId}</p>
                  <p className='text-[#6F6F6E] text-[16px] font-medium'>登録カード番号: {gmoData.cardNo}</p>
                </div>
              )}
          </div>
          
          {/* Action Button */}
          <div className='bg-[#1D9987] hover:bg-[#1D9987] transition-colors cursor-pointer w-full lg:w-2/3 md:w-1/2 mx-auto rounded-md'
               onClick={() => gmoData && (gmoData.memberId !== null || gmoData.cardNo !== null) ? handleCreateLinkplusUrl() : handlePaymentGMO()}>
              <div className='px-4 py-2 text-center'>
                  <h2 className='text-white font-medium flex items-center justify-center gap-2'>
                      <span className='text-3xl'>▶︎</span>
                      <span>
                          {gmoData && (gmoData.memberId !== null || gmoData.cardNo !== null) ? 'クレジットカード編集はこちら' : 'クレジットカード登録はこちら'}
                      </span>
                  </h2>
              </div>
          </div>
        </div>
      )}
    </div>
  );
};